package testbackend

import (
	"fmt"
	"io/ioutil"
	"net/http"
)

func main() {

	url := "http://10.27.141.30:8181/api/external/deliver/import?page_size=-1&page_no=1&scope=delivery&scope_id=1"
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, nil)

	if err != nil {
		fmt.Println(err)
		return
	}
	req.Header.Add("User-Agent", "iAPI/1.0.0 (https://iapi.baidu-int.com)")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(string(body))
}
