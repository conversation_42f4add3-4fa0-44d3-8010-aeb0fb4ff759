package testbackend

import (
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	// 配置参数 - 实际项目中应该从配置文件或环境变量读取
	url := "http:www.http.bin"
	method := "GET"

	// 创建带超时的HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second, // 30秒超时
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	// 设置请求头
	req.Header.Set("User-Agent", "iAPI/1.0.0 (https://iapi.baidu-int.com)")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// 执行HTTP请求
	fmt.Printf("正在请求: %s\n", url)
	res, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求执行失败: %v\n", err)
		return
	}
	defer res.Body.Close()

	// 检查HTTP状态码
	if res.StatusCode < 200 || res.StatusCode >= 300 {
		fmt.Printf("请求失败，状态码: %d %s\n", res.StatusCode, res.Status)
		return
	}

	// 读取响应体
	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Printf("读取响应体失败: %v\n", err)
		return
	}

	// 输出结果
	fmt.Printf("请求成功，状态码: %d\n", res.StatusCode)
	fmt.Printf("响应内容:\n%s\n", string(body))
}
