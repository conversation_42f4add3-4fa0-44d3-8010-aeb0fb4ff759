package controllers

import (
	"net/http"
	"strconv"
	"student-grade-system/config"
	"student-grade-system/models"

	"github.com/gin-gonic/gin"
)

// GetStudents 获取学生列表
func GetStudents(c *gin.Context) {
	var students []models.Student
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c<PERSON>("page_size", "10"))
	search := c.Query("search")

	query := config.DB.Model(&models.Student{})

	// 搜索功能
	if search != "" {
		query = query.Where("name LIKE ? OR student_no LIKE ? OR class LIKE ?", 
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 分页
	offset := (page - 1) * pageSize
	var total int64
	query.Count(&total)

	if err := query.Offset(offset).Limit(pageSize).Find(&students).Error; err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取学生列表失败",
			"error":   err.<PERSON>rror(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"students":   students,
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetStudent 获取单个学生信息
func GetStudent(c *gin.Context) {
	id := c.Param("id")
	var student models.Student

	if err := config.DB.Preload("Grades.Course").First(&student, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "学生不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    student,
	})
}

// CreateStudent 创建学生
func CreateStudent(c *gin.Context) {
	var student models.Student
	if err := c.ShouldBindJSON(&student); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := config.DB.Create(&student).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建学生失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    student,
	})
}

// UpdateStudent 更新学生信息
func UpdateStudent(c *gin.Context) {
	id := c.Param("id")
	var student models.Student

	if err := config.DB.First(&student, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "学生不存在",
		})
		return
	}

	if err := c.ShouldBindJSON(&student); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := config.DB.Save(&student).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新学生信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    student,
	})
}

// DeleteStudent 删除学生
func DeleteStudent(c *gin.Context) {
	id := c.Param("id")
	var student models.Student

	if err := config.DB.First(&student, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "学生不存在",
		})
		return
	}

	if err := config.DB.Delete(&student).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除学生失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}
