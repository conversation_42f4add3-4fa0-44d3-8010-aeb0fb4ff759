package controllers

import (
	"net/http"
	"strconv"
	"student-grade-system/config"
	"student-grade-system/models"

	"github.com/gin-gonic/gin"
)

// GetGrades 获取成绩列表
func GetGrades(c *gin.Context) {
	var grades []models.Grade
	page, _ := strconv.Atoi(c<PERSON><PERSON><PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))
	studentID := c.Query("student_id")
	courseID := c.Query("course_id")

	query := config.DB.Model(&models.Grade{}).Preload("Student").Preload("Course")

	// 按学生ID筛选
	if studentID != "" {
		query = query.Where("student_id = ?", studentID)
	}

	// 按课程ID筛选
	if courseID != "" {
		query = query.Where("course_id = ?", courseID)
	}

	// 分页
	offset := (page - 1) * pageSize
	var total int64
	query.Count(&total)

	if err := query.Offset(offset).Limit(pageSize).Find(&grades).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取成绩列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"grades":     grades,
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetGrade 获取单个成绩信息
func GetGrade(c *gin.Context) {
	id := c.Param("id")
	var grade models.Grade

	if err := config.DB.Preload("Student").Preload("Course").First(&grade, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "成绩不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    grade,
	})
}

// CreateGrade 创建成绩
func CreateGrade(c *gin.Context) {
	var grade models.Grade
	if err := c.ShouldBindJSON(&grade); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 自动计算等级
	grade.CalculateGradeLevel()

	if err := config.DB.Create(&grade).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建成绩失败",
			"error":   err.Error(),
		})
		return
	}

	// 重新查询以获取关联数据
	config.DB.Preload("Student").Preload("Course").First(&grade, grade.ID)

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    grade,
	})
}

// UpdateGrade 更新成绩信息
func UpdateGrade(c *gin.Context) {
	id := c.Param("id")
	var grade models.Grade

	if err := config.DB.First(&grade, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "成绩不存在",
		})
		return
	}

	if err := c.ShouldBindJSON(&grade); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 重新计算等级
	grade.CalculateGradeLevel()

	if err := config.DB.Save(&grade).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新成绩信息失败",
			"error":   err.Error(),
		})
		return
	}

	// 重新查询以获取关联数据
	config.DB.Preload("Student").Preload("Course").First(&grade, grade.ID)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    grade,
	})
}

// DeleteGrade 删除成绩
func DeleteGrade(c *gin.Context) {
	id := c.Param("id")
	var grade models.Grade

	if err := config.DB.First(&grade, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "成绩不存在",
		})
		return
	}

	if err := config.DB.Delete(&grade).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除成绩失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetStudentGrades 获取学生的所有成绩
func GetStudentGrades(c *gin.Context) {
	studentID := c.Param("student_id")
	var grades []models.Grade

	if err := config.DB.Where("student_id = ?", studentID).Preload("Course").Find(&grades).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取学生成绩失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    grades,
	})
}
