package config

import (
	"log"
	"student-grade-system/models"
	"student-grade-system/utils"
	"time"
)

// InitData 初始化数据库数据
func InitData() {
	// 检查是否已有用户数据，如果有则跳过初始化
	var userCount int64
	DB.Model(&models.User{}).Count(&userCount)
	if userCount > 0 {
		log.Println("数据库已有数据，跳过初始化")
		return
	}

	log.Println("开始初始化数据库数据...")

	// 创建默认管理员用户
	adminUser := models.User{
		Username: "admin",
		Password: utils.HashPassword("123456"),
		Email:    "<EMAIL>",
		Role:     "admin",
	}
	if err := DB.Create(&adminUser).Error; err != nil {
		log.Printf("创建管理员用户失败: %v", err)
	} else {
		log.Println("创建管理员用户成功: admin/123456")
	}

	// 创建示例学生数据
	students := []models.Student{
		{
			StudentNo:  "2024001",
			Name:       "张三",
			Gender:     "male",
			Age:        20,
			Class:      "计算机1班",
			Major:      "计算机科学",
			Phone:      "13800138001",
			Email:      "<EMAIL>",
			Address:    "北京市海淀区",
			EnrollYear: 2024,
			Status:     "active",
		},
		{
			StudentNo:  "2024002",
			Name:       "李四",
			Gender:     "female",
			Age:        19,
			Class:      "计算机1班",
			Major:      "计算机科学",
			Phone:      "13800138002",
			Email:      "<EMAIL>",
			Address:    "上海市浦东新区",
			EnrollYear: 2024,
			Status:     "active",
		},
		{
			StudentNo:  "2024003",
			Name:       "王五",
			Gender:     "male",
			Age:        21,
			Class:      "计算机2班",
			Major:      "软件工程",
			Phone:      "13800138003",
			Email:      "<EMAIL>",
			Address:    "广州市天河区",
			EnrollYear: 2024,
			Status:     "active",
		},
		{
			StudentNo:  "2024004",
			Name:       "赵六",
			Gender:     "female",
			Age:        20,
			Class:      "计算机2班",
			Major:      "软件工程",
			Phone:      "13800138004",
			Email:      "<EMAIL>",
			Address:    "深圳市南山区",
			EnrollYear: 2024,
			Status:     "active",
		},
	}

	for _, student := range students {
		if err := DB.Create(&student).Error; err != nil {
			log.Printf("创建学生 %s 失败: %v", student.Name, err)
		}
	}
	log.Printf("创建了 %d 个示例学生", len(students))

	// 创建示例课程数据
	courses := []models.Course{
		{
			CourseCode:  "CS101",
			CourseName:  "计算机基础",
			Credits:     3.0,
			Teacher:     "张教授",
			Department:  "计算机学院",
			Semester:    "2024春季",
			Description: "计算机科学与技术基础课程，包括计算机系统概论、程序设计基础等内容。",
			Status:      "active",
		},
		{
			CourseCode:  "CS102",
			CourseName:  "数据结构",
			Credits:     4.0,
			Teacher:     "李教授",
			Department:  "计算机学院",
			Semester:    "2024春季",
			Description: "数据结构与算法分析，包括线性表、树、图等数据结构及相关算法。",
			Status:      "active",
		},
		{
			CourseCode:  "CS103",
			CourseName:  "数据库原理",
			Credits:     3.5,
			Teacher:     "王教授",
			Department:  "计算机学院",
			Semester:    "2024春季",
			Description: "数据库系统的基本概念、设计方法和应用技术。",
			Status:      "active",
		},
		{
			CourseCode:  "CS104",
			CourseName:  "操作系统",
			Credits:     4.0,
			Teacher:     "陈教授",
			Department:  "计算机学院",
			Semester:    "2024秋季",
			Description: "操作系统的基本原理、设计方法和实现技术。",
			Status:      "active",
		},
		{
			CourseCode:  "CS105",
			CourseName:  "计算机网络",
			Credits:     3.5,
			Teacher:     "刘教授",
			Department:  "计算机学院",
			Semester:    "2024秋季",
			Description: "计算机网络的基本概念、协议和应用。",
			Status:      "active",
		},
	}

	for _, course := range courses {
		if err := DB.Create(&course).Error; err != nil {
			log.Printf("创建课程 %s 失败: %v", course.CourseName, err)
		}
	}
	log.Printf("创建了 %d 个示例课程", len(courses))

	// 创建示例成绩数据
	examDate1, _ := time.Parse("2006-01-02", "2024-06-15")
	examDate2, _ := time.Parse("2006-01-02", "2024-06-16")
	examDate3, _ := time.Parse("2006-01-02", "2024-06-17")

	grades := []models.Grade{
		{
			StudentID:  1,
			CourseID:   1,
			Score:      85.5,
			ExamType:   "final",
			ExamDate:   &examDate1,
			Remarks:    "表现良好，基础扎实",
		},
		{
			StudentID:  1,
			CourseID:   2,
			Score:      92.0,
			ExamType:   "final",
			ExamDate:   &examDate2,
			Remarks:    "优秀，算法理解深入",
		},
		{
			StudentID:  2,
			CourseID:   1,
			Score:      78.0,
			ExamType:   "final",
			ExamDate:   &examDate1,
			Remarks:    "需要加强基础练习",
		},
		{
			StudentID:  2,
			CourseID:   2,
			Score:      88.5,
			ExamType:   "final",
			ExamDate:   &examDate2,
			Remarks:    "进步明显，继续努力",
		},
		{
			StudentID:  3,
			CourseID:   1,
			Score:      90.0,
			ExamType:   "final",
			ExamDate:   &examDate1,
			Remarks:    "优秀，理论基础扎实",
		},
		{
			StudentID:  3,
			CourseID:   3,
			Score:      87.5,
			ExamType:   "final",
			ExamDate:   &examDate3,
			Remarks:    "数据库设计能力强",
		},
		{
			StudentID:  4,
			CourseID:   2,
			Score:      82.0,
			ExamType:   "final",
			ExamDate:   &examDate2,
			Remarks:    "算法实现能力良好",
		},
		{
			StudentID:  4,
			CourseID:   3,
			Score:      89.0,
			ExamType:   "final",
			ExamDate:   &examDate3,
			Remarks:    "SQL语句掌握熟练",
		},
	}

	for _, grade := range grades {
		// 自动计算等级
		grade.CalculateGradeLevel()
		if err := DB.Create(&grade).Error; err != nil {
			log.Printf("创建成绩记录失败: %v", err)
		}
	}
	log.Printf("创建了 %d 条示例成绩记录", len(grades))

	log.Println("数据库初始化完成！")
	log.Println("默认管理员账号: admin")
	log.Println("默认管理员密码: 123456")
}
