package config

import (
	"log"
	"os"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

var DB *gorm.DB

func InitDB() {
	// 获取数据库文件路径，默认为当前目录下的database.db
	dbPath := os.Getenv("DB_PATH")
	if dbPath == "" {
		dbPath = "./database.db"
	}

	var err error
	DB, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	log.Println("SQLite数据库连接成功，数据库文件:", dbPath)
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}
