package models

import (
	"time"

	"gorm.io/gorm"
)

// Grade 成绩模型
type Grade struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	StudentID  uint           `json:"student_id" gorm:"not null;index"`         // 学生ID
	CourseID   uint           `json:"course_id" gorm:"not null;index"`          // 课程ID
	Score      float32        `json:"score" gorm:"not null"`                    // 分数
	GradeLevel string         `json:"grade_level"`                              // 等级 (A, B, C, D, F)
	ExamType   string         `json:"exam_type" gorm:"default:final"`           // 考试类型: midterm, final, quiz, assignment
	ExamDate   *time.Time     `json:"exam_date"`                                // 考试日期
	Remarks    string         `json:"remarks"`                                  // 备注
	Student    Student        `json:"student,omitempty" gorm:"foreignKey:StudentID"`
	Course     Course         `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Grade) TableName() string {
	return "grades"
}

// CalculateGradeLevel 根据分数计算等级
func (g *Grade) CalculateGradeLevel() {
	switch {
	case g.Score >= 90:
		g.GradeLevel = "A"
	case g.Score >= 80:
		g.GradeLevel = "B"
	case g.Score >= 70:
		g.GradeLevel = "C"
	case g.Score >= 60:
		g.GradeLevel = "D"
	default:
		g.GradeLevel = "F"
	}
}
