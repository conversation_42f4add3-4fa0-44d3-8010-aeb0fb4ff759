package models

import (
	"time"

	"gorm.io/gorm"
)

// Course 课程模型
type Course struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	CourseCode  string         `json:"course_code" gorm:"uniqueIndex;not null"` // 课程代码
	CourseName  string         `json:"course_name" gorm:"not null"`              // 课程名称
	Credits     float32        `json:"credits" gorm:"not null"`                  // 学分
	Teacher     string         `json:"teacher"`                                  // 授课教师
	Department  string         `json:"department"`                               // 开课院系
	Semester    string         `json:"semester"`                                 // 学期
	Description string         `json:"description"`                              // 课程描述
	Status      string         `json:"status" gorm:"default:active"`            // 状态: active, inactive
	Grades      []Grade        `json:"grades,omitempty" gorm:"foreignKey:CourseID"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Course) TableName() string {
	return "courses"
}
