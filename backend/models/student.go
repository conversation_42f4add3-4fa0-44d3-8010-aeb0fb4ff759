package models

import (
	"time"

	"gorm.io/gorm"
)

// Student 学生模型
type Student struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	StudentNo   string         `json:"student_no" gorm:"uniqueIndex;not null"` // 学号
	Name        string         `json:"name" gorm:"not null"`                   // 姓名
	Gender      string         `json:"gender" gorm:"default:male"`             // 性别
	Age         int            `json:"age"`                                     // 年龄
	Class       string         `json:"class"`                                   // 班级
	Major       string         `json:"major"`                                   // 专业
	Phone       string         `json:"phone"`                                   // 电话
	Email       string         `json:"email"`                                   // 邮箱
	Address     string         `json:"address"`                                 // 地址
	EnrollYear  int            `json:"enroll_year"`                            // 入学年份
	Status      string         `json:"status" gorm:"default:active"`           // 状态: active, inactive, graduated
	Grades      []Grade        `json:"grades,omitempty" gorm:"foreignKey:StudentID"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Student) TableName() string {
	return "students"
}
