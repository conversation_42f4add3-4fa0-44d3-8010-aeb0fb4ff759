package main

import (
	"fmt"
	"log"
	"student-grade-system/config"
	"student-grade-system/models"
	"student-grade-system/routes"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化数据库
	config.InitDB()

	// 自动迁移数据库表
	err := config.DB.AutoMigrate(&models.User{}, &models.Student{}, &models.Course{}, &models.Grade{})
	if err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	// 初始化示例数据
	config.InitData()

	// 创建Gin引擎
	r := gin.Default()

	// 配置CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:8080"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// 设置路由
	routes.SetupRoutes(r)

	// 启动服务器
	fmt.Println("🎉 学生成绩管理系统启动成功！")
	fmt.Println("📊 数据库类型: SQLite")
	fmt.Println("📁 数据库文件: ./database.db")
	fmt.Println("🌐 前端地址: http://localhost:3001")
	fmt.Println("🔗 后端地址: http://localhost:8000")
	fmt.Println("👤 登录信息: 用户名: admin, 密码: 123456")
	fmt.Println("🚀 服务器运行在端口 :8000")

	log.Fatal(r.Run(":8000"))
}
