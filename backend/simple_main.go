package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"
)

// 简单的数据结构
type Student struct {
	ID         int    `json:"id"`
	StudentNo  string `json:"student_no"`
	Name       string `json:"name"`
	Gender     string `json:"gender"`
	Age        int    `json:"age"`
	Class      string `json:"class"`
	Major      string `json:"major"`
	Phone      string `json:"phone"`
	Email      string `json:"email"`
	Address    string `json:"address"`
	EnrollYear int    `json:"enroll_year"`
	Status     string `json:"status"`
	CreatedAt  string `json:"created_at"`
}

type Course struct {
	ID          int    `json:"id"`
	CourseCode  string `json:"course_code"`
	CourseName  string `json:"course_name"`
	Credits     float32 `json:"credits"`
	Teacher     string `json:"teacher"`
	Department  string `json:"department"`
	Semester    string `json:"semester"`
	Description string `json:"description"`
	Status      string `json:"status"`
	CreatedAt   string `json:"created_at"`
}

type Grade struct {
	ID         int     `json:"id"`
	StudentID  int     `json:"student_id"`
	CourseID   int     `json:"course_id"`
	Score      float32 `json:"score"`
	GradeLevel string  `json:"grade_level"`
	ExamType   string  `json:"exam_type"`
	ExamDate   string  `json:"exam_date"`
	Remarks    string  `json:"remarks"`
	Student    *Student `json:"student,omitempty"`
	Course     *Course  `json:"course,omitempty"`
	CreatedAt  string  `json:"created_at"`
}

type User struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Role     string `json:"role"`
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 模拟数据存储
var students = []Student{
	{ID: 1, StudentNo: "2024001", Name: "张三", Gender: "male", Age: 20, Class: "计算机1班", Major: "计算机科学", Phone: "13800138001", Email: "<EMAIL>", Address: "北京市", EnrollYear: 2024, Status: "active", CreatedAt: "2024-01-01T00:00:00Z"},
	{ID: 2, StudentNo: "2024002", Name: "李四", Gender: "female", Age: 19, Class: "计算机1班", Major: "计算机科学", Phone: "13800138002", Email: "<EMAIL>", Address: "上海市", EnrollYear: 2024, Status: "active", CreatedAt: "2024-01-01T00:00:00Z"},
	{ID: 3, StudentNo: "2024003", Name: "王五", Gender: "male", Age: 21, Class: "计算机2班", Major: "软件工程", Phone: "13800138003", Email: "<EMAIL>", Address: "广州市", EnrollYear: 2024, Status: "active", CreatedAt: "2024-01-01T00:00:00Z"},
}

var courses = []Course{
	{ID: 1, CourseCode: "CS101", CourseName: "计算机基础", Credits: 3.0, Teacher: "张教授", Department: "计算机学院", Semester: "2024春季", Description: "计算机基础课程", Status: "active", CreatedAt: "2024-01-01T00:00:00Z"},
	{ID: 2, CourseCode: "CS102", CourseName: "数据结构", Credits: 4.0, Teacher: "李教授", Department: "计算机学院", Semester: "2024春季", Description: "数据结构与算法", Status: "active", CreatedAt: "2024-01-01T00:00:00Z"},
	{ID: 3, CourseCode: "CS103", CourseName: "数据库原理", Credits: 3.5, Teacher: "王教授", Department: "计算机学院", Semester: "2024春季", Description: "数据库设计与应用", Status: "active", CreatedAt: "2024-01-01T00:00:00Z"},
}

var grades = []Grade{
	{ID: 1, StudentID: 1, CourseID: 1, Score: 85.5, GradeLevel: "B", ExamType: "final", ExamDate: "2024-06-15", Remarks: "表现良好", CreatedAt: "2024-06-16T00:00:00Z"},
	{ID: 2, StudentID: 1, CourseID: 2, Score: 92.0, GradeLevel: "A", ExamType: "final", ExamDate: "2024-06-16", Remarks: "优秀", CreatedAt: "2024-06-17T00:00:00Z"},
	{ID: 3, StudentID: 2, CourseID: 1, Score: 78.0, GradeLevel: "C", ExamType: "final", ExamDate: "2024-06-15", Remarks: "需要改进", CreatedAt: "2024-06-16T00:00:00Z"},
}

var nextStudentID = 4
var nextCourseID = 4
var nextGradeID = 4

// CORS中间件
func corsMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")
		
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		next(w, r)
	}
}

// 响应辅助函数
func sendResponse(w http.ResponseWriter, code int, message string, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	response := Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
	json.NewEncoder(w).Encode(response)
}

// 登录处理
func loginHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		sendResponse(w, 405, "Method not allowed", nil)
		return
	}
	
	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendResponse(w, 400, "Invalid request body", nil)
		return
	}
	
	// 简单的用户验证
	if req.Username == "admin" && req.Password == "123456" {
		user := User{
			ID:       1,
			Username: "admin",
			Email:    "<EMAIL>",
			Role:     "admin",
		}
		
		data := map[string]interface{}{
			"token": "mock-jwt-token-" + strconv.FormatInt(time.Now().Unix(), 10),
			"user":  user,
		}
		
		sendResponse(w, 200, "登录成功", data)
	} else {
		sendResponse(w, 401, "用户名或密码错误", nil)
	}
}

// 学生相关处理
func studentsHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "GET":
		data := map[string]interface{}{
			"students":   students,
			"total":      len(students),
			"page":       1,
			"page_size":  10,
			"total_page": 1,
		}
		sendResponse(w, 200, "获取成功", data)
	case "POST":
		var student Student
		if err := json.NewDecoder(r.Body).Decode(&student); err != nil {
			sendResponse(w, 400, "Invalid request body", nil)
			return
		}
		
		student.ID = nextStudentID
		nextStudentID++
		student.CreatedAt = time.Now().Format(time.RFC3339)
		students = append(students, student)
		
		sendResponse(w, 201, "创建成功", student)
	default:
		sendResponse(w, 405, "Method not allowed", nil)
	}
}

// 课程相关处理
func coursesHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "GET":
		data := map[string]interface{}{
			"courses":    courses,
			"total":      len(courses),
			"page":       1,
			"page_size":  10,
			"total_page": 1,
		}
		sendResponse(w, 200, "获取成功", data)
	case "POST":
		var course Course
		if err := json.NewDecoder(r.Body).Decode(&course); err != nil {
			sendResponse(w, 400, "Invalid request body", nil)
			return
		}
		
		course.ID = nextCourseID
		nextCourseID++
		course.CreatedAt = time.Now().Format(time.RFC3339)
		courses = append(courses, course)
		
		sendResponse(w, 201, "创建成功", course)
	default:
		sendResponse(w, 405, "Method not allowed", nil)
	}
}

// 成绩相关处理
func gradesHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "GET":
		// 填充学生和课程信息
		var enrichedGrades []Grade
		for _, grade := range grades {
			enrichedGrade := grade
			
			// 查找学生信息
			for _, student := range students {
				if student.ID == grade.StudentID {
					enrichedGrade.Student = &student
					break
				}
			}
			
			// 查找课程信息
			for _, course := range courses {
				if course.ID == grade.CourseID {
					enrichedGrade.Course = &course
					break
				}
			}
			
			enrichedGrades = append(enrichedGrades, enrichedGrade)
		}
		
		data := map[string]interface{}{
			"grades":     enrichedGrades,
			"total":      len(enrichedGrades),
			"page":       1,
			"page_size":  10,
			"total_page": 1,
		}
		sendResponse(w, 200, "获取成功", data)
	case "POST":
		var grade Grade
		if err := json.NewDecoder(r.Body).Decode(&grade); err != nil {
			sendResponse(w, 400, "Invalid request body", nil)
			return
		}
		
		grade.ID = nextGradeID
		nextGradeID++
		grade.CreatedAt = time.Now().Format(time.RFC3339)
		
		// 自动计算等级
		if grade.Score >= 90 {
			grade.GradeLevel = "A"
		} else if grade.Score >= 80 {
			grade.GradeLevel = "B"
		} else if grade.Score >= 70 {
			grade.GradeLevel = "C"
		} else if grade.Score >= 60 {
			grade.GradeLevel = "D"
		} else {
			grade.GradeLevel = "F"
		}
		
		grades = append(grades, grade)
		
		sendResponse(w, 201, "创建成功", grade)
	default:
		sendResponse(w, 405, "Method not allowed", nil)
	}
}

func main() {
	// 设置路由
	http.HandleFunc("/api/v1/auth/login", corsMiddleware(loginHandler))
	http.HandleFunc("/api/v1/students", corsMiddleware(studentsHandler))
	http.HandleFunc("/api/v1/courses", corsMiddleware(coursesHandler))
	http.HandleFunc("/api/v1/grades", corsMiddleware(gradesHandler))
	
	fmt.Println("服务器启动在端口 :8000")
	fmt.Println("前端地址: http://localhost:3000")
	fmt.Println("后端地址: http://localhost:8000")
	fmt.Println("登录信息: 用户名: admin, 密码: 123456")
	
	log.Fatal(http.ListenAndServe(":8000", nil))
}
