<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>vue-demo01</title>
</head>

<body>

<div id="app">
    <div v-for="(name,i) in arrays">
        <li>{{i}}:{{name}}</li>
    </div>
</div>
<script src="https://cdn.bootcss.com/vue/2.5.16/vue.min.js"></script>
<script>
    // 1.创建vue核心对象
    new Vue({
        el: "#app",
        data() {
            return {
                arrays: ["mayikt", "meite", "wangmazi"]
            }
        }
    });
</script>
</body>
</html>