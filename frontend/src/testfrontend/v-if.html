<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>vue-demo01</title>
</head>

<body>

<div id="app">
    <div v-if="state==1">
        div1成功显示
    </div>
    <div v-else>
        其他div成功显示
    </div>
    <input type="text" v-model="state">

    <div v-show="showState==2">显示 show div</div>
</div>
<script src="https://cdn.bootcss.com/vue/2.5.16/vue.min.js"></script>
<script>
    // 1.创建vue核心对象
    new Vue({
        el: "#app",
        data() {
            return {
                username: "mayikt",
                url: "http://www.mayikt.com",
                state: 1,
                showState:3
            }
        },
        methods: {
            mayiktShow() {
                alert("我是mayikt");
            },
            mayiktShow2() {
                alert("我是meite");
            }
        }
    });
</script>
</body>
</html>