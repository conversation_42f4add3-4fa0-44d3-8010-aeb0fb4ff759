<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>vue-demo01</title>
</head>

<body>

<div id="app">
    <a v-bind:href="url">点击跳转www.mayikt.com</a>
    <a :href="url">简写点击跳转www.mayikt.com</a>
    <input type="text" v-model="username">
    {{username}}
</div>
<script src="https://cdn.bootcss.com/vue/2.5.16/vue.min.js"></script>
<script>
    // 1.创建vue核心对象
    new Vue({
        el: "#app",
        data() {
            return {
                username: "mayikt",
                url: "http://www.mayikt.com"
            }
        }
    });
</script>
</body>
</html>