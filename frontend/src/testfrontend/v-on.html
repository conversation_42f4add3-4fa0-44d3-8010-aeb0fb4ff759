<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
</head>
<body>
<div id="app">
    <input type="button" value="点击我" v-on:click="mayiktShow()">
    <input type="button"  value="点击我" @click="mayiktShow()">
</div>
<script src="https://cdn.bootcss.com/vue/2.5.16/vue.min.js"></script>
<script>
    // 1.创建vue核心对象
    new Vue({
        el: "#app",
        data() {
            return {
                username: "mayikt",
                url: "http://www.mayikt.com"
            }
        },
        methods: {
            mayiktShow() {
                alert("我是mayikt");
            }
        }
    });
</script>
</body>
</html>