<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>vue-demo01</title>
</head>

<body>

<div id="app">
    <input type="text" name="username" v-model="username">
    <!-- 插值表达式 -->
    {{username}}
</div>
<script src="https://cdn.bootcss.com/vue/2.5.16/vue.min.js"></script>
<script>
    // 1.创建vue核心对象
    new Vue({
        el: "#app",
        data() {
            return {
                username: "mayikt"
            }
        }
    });
</script>
</body>
</html>