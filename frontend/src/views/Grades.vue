<template>
  <div class="grades-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>成绩管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            录入成绩
          </el-button>
        </div>
      </template>

      <!-- 筛选栏 -->
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select
              v-model="filterForm.student_id"
              placeholder="选择学生"
              clearable
              filterable
              @change="handleFilter"
            >
              <el-option
                v-for="student in allStudents"
                :key="student.id"
                :label="`${student.name} (${student.student_no})`"
                :value="student.id"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterForm.course_id"
              placeholder="选择课程"
              clearable
              filterable
              @change="handleFilter"
            >
              <el-option
                v-for="course in allCourses"
                :key="course.id"
                :label="`${course.course_name} (${course.course_code})`"
                :value="course.id"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterForm.exam_type"
              placeholder="考试类型"
              clearable
              @change="handleFilter"
            >
              <el-option label="期中考试" value="midterm" />
              <el-option label="期末考试" value="final" />
              <el-option label="随堂测验" value="quiz" />
              <el-option label="作业" value="assignment" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button @click="resetFilter">重置筛选</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 成绩表格 -->
      <el-table
        :data="grades"
        style="width: 100%"
        v-loading="loading"
        stripe
      >
        <el-table-column prop="student.name" label="学生姓名" width="120" />
        <el-table-column prop="student.student_no" label="学号" width="120" />
        <el-table-column prop="course.course_name" label="课程名称" width="180" />
        <el-table-column prop="course.course_code" label="课程代码" width="120" />
        <el-table-column prop="score" label="分数" width="80" />
        <el-table-column prop="grade_level" label="等级" width="80">
          <template #default="scope">
            <el-tag :type="getGradeType(scope.row.grade_level)">
              {{ scope.row.grade_level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="exam_type" label="考试类型" width="100">
          <template #default="scope">
            {{ getExamTypeText(scope.row.exam_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="exam_date" label="考试日期" width="120">
          <template #default="scope">
            {{ scope.row.exam_date ? formatDate(scope.row.exam_date) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="warning" size="small" @click="editGrade(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteGrade(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑成绩对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑成绩' : '录入成绩'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="gradeFormRef"
        :model="gradeForm"
        :rules="gradeRules"
        label-width="80px"
      >
        <el-form-item label="学生" prop="student_id">
          <el-select
            v-model="gradeForm.student_id"
            placeholder="选择学生"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="student in allStudents"
              :key="student.id"
              :label="`${student.name} (${student.student_no})`"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="课程" prop="course_id">
          <el-select
            v-model="gradeForm.course_id"
            placeholder="选择课程"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="course in allCourses"
              :key="course.id"
              :label="`${course.course_name} (${course.course_code})`"
              :value="course.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分数" prop="score">
          <el-input-number
            v-model="gradeForm.score"
            :min="0"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="考试类型" prop="exam_type">
          <el-select v-model="gradeForm.exam_type" style="width: 100%">
            <el-option label="期中考试" value="midterm" />
            <el-option label="期末考试" value="final" />
            <el-option label="随堂测验" value="quiz" />
            <el-option label="作业" value="assignment" />
          </el-select>
        </el-form-item>
        <el-form-item label="考试日期" prop="exam_date">
          <el-date-picker
            v-model="gradeForm.exam_date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="gradeForm.remarks"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '录入' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getGrades, createGrade, updateGrade, deleteGrade as deleteGradeApi } from '@/api/grade'
import { getStudents } from '@/api/student'
import { getCourses } from '@/api/course'

const loading = ref(false)
const submitLoading = ref(false)
const grades = ref([])
const allStudents = ref([])
const allCourses = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

const showAddDialog = ref(false)
const isEdit = ref(false)
const currentGrade = ref(null)
const gradeFormRef = ref()

const filterForm = reactive({
  student_id: '',
  course_id: '',
  exam_type: ''
})

const gradeForm = reactive({
  student_id: '',
  course_id: '',
  score: 0,
  exam_type: 'final',
  exam_date: '',
  remarks: ''
})

const gradeRules = {
  student_id: [
    { required: true, message: '请选择学生', trigger: 'change' }
  ],
  course_id: [
    { required: true, message: '请选择课程', trigger: 'change' }
  ],
  score: [
    { required: true, message: '请输入分数', trigger: 'blur' }
  ],
  exam_type: [
    { required: true, message: '请选择考试类型', trigger: 'change' }
  ]
}

// 加载成绩列表
const loadGrades = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...filterForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (!params[key]) {
        delete params[key]
      }
    })

    const response = await getGrades(params)
    grades.value = response.data.grades
    total.value = response.data.total
  } catch (error) {
    console.error('加载成绩列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载学生和课程数据
const loadBasicData = async () => {
  try {
    const [studentsRes, coursesRes] = await Promise.all([
      getStudents({ page: 1, page_size: 1000 }),
      getCourses({ page: 1, page_size: 1000 })
    ])

    allStudents.value = studentsRes.data.students
    allCourses.value = coursesRes.data.courses
  } catch (error) {
    console.error('加载基础数据失败:', error)
  }
}

// 筛选处理
const handleFilter = () => {
  currentPage.value = 1
  loadGrades()
}

// 重置筛选
const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  handleFilter()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadGrades()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadGrades()
}

// 编辑成绩
const editGrade = (grade) => {
  isEdit.value = true
  currentGrade.value = grade
  Object.keys(gradeForm).forEach(key => {
    if (key === 'exam_date' && grade[key]) {
      gradeForm[key] = new Date(grade[key])
    } else {
      gradeForm[key] = grade[key] || ''
    }
  })
  showAddDialog.value = true
}

// 删除成绩
const deleteGrade = async (grade) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条成绩记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteGradeApi(grade.id)
    ElMessage.success('删除成功')
    loadGrades()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除成绩失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!gradeFormRef.value) return

  await gradeFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        const submitData = { ...gradeForm }
        if (submitData.exam_date) {
          submitData.exam_date = submitData.exam_date.toISOString().split('T')[0]
        }

        if (isEdit.value) {
          await updateGrade(currentGrade.value.id, submitData)
          ElMessage.success('更新成功')
        } else {
          await createGrade(submitData)
          ElMessage.success('录入成功')
        }
        showAddDialog.value = false
        loadGrades()
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  currentGrade.value = null
  Object.keys(gradeForm).forEach(key => {
    if (key === 'score') {
      gradeForm[key] = 0
    } else if (key === 'exam_type') {
      gradeForm[key] = 'final'
    } else {
      gradeForm[key] = ''
    }
  })
  if (gradeFormRef.value) {
    gradeFormRef.value.clearValidate()
  }
}

// 获取等级类型
const getGradeType = (grade) => {
  const gradeTypes = {
    'A': 'success',
    'B': 'primary',
    'C': 'warning',
    'D': 'info',
    'F': 'danger'
  }
  return gradeTypes[grade] || 'info'
}

// 获取考试类型文本
const getExamTypeText = (type) => {
  const typeTexts = {
    'midterm': '期中考试',
    'final': '期末考试',
    'quiz': '随堂测验',
    'assignment': '作业'
  }
  return typeTexts[type] || type
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadBasicData()
  loadGrades()
})
</script>

<style scoped>
.grades-page {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
