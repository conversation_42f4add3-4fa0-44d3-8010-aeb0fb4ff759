<template>
  <div class="dashboard">
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon student-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.studentCount }}</div>
              <div class="stats-label">学生总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon course-icon">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.courseCount }}</div>
              <div class="stats-label">课程总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon grade-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.gradeCount }}</div>
              <div class="stats-label">成绩记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon avg-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.avgScore }}</div>
              <div class="stats-label">平均分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近添加的学生</span>
              <el-button type="text" @click="$router.push('/students')">查看更多</el-button>
            </div>
          </template>
          <el-table :data="recentStudents" style="width: 100%">
            <el-table-column prop="student_no" label="学号" width="120" />
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="class" label="班级" />
            <el-table-column prop="created_at" label="添加时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近录入的成绩</span>
              <el-button type="text" @click="$router.push('/grades')">查看更多</el-button>
            </div>
          </template>
          <el-table :data="recentGrades" style="width: 100%">
            <el-table-column prop="student.name" label="学生" />
            <el-table-column prop="course.course_name" label="课程" />
            <el-table-column prop="score" label="分数" width="80" />
            <el-table-column prop="grade_level" label="等级" width="80">
              <template #default="scope">
                <el-tag :type="getGradeType(scope.row.grade_level)">
                  {{ scope.row.grade_level }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getStudents } from '@/api/student'
import { getCourses } from '@/api/course'
import { getGrades } from '@/api/grade'
import { User, Reading, Document, TrendCharts } from '@element-plus/icons-vue'

const stats = ref({
  studentCount: 0,
  courseCount: 0,
  gradeCount: 0,
  avgScore: 0
})

const recentStudents = ref([])
const recentGrades = ref([])

const loadStats = async () => {
  try {
    // 获取学生统计
    const studentsRes = await getStudents({ page: 1, page_size: 5 })
    stats.value.studentCount = studentsRes.data.total
    recentStudents.value = studentsRes.data.students

    // 获取课程统计
    const coursesRes = await getCourses({ page: 1, page_size: 1 })
    stats.value.courseCount = coursesRes.data.total

    // 获取成绩统计
    const gradesRes = await getGrades({ page: 1, page_size: 5 })
    stats.value.gradeCount = gradesRes.data.total
    recentGrades.value = gradesRes.data.grades

    // 计算平均分
    if (gradesRes.data.grades.length > 0) {
      const totalScore = gradesRes.data.grades.reduce((sum, grade) => sum + grade.score, 0)
      stats.value.avgScore = (totalScore / gradesRes.data.grades.length).toFixed(1)
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getGradeType = (grade) => {
  const gradeTypes = {
    'A': 'success',
    'B': 'primary',
    'C': 'warning',
    'D': 'info',
    'F': 'danger'
  }
  return gradeTypes[grade] || 'info'
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.student-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.course-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.grade-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.avg-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.content-row {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
