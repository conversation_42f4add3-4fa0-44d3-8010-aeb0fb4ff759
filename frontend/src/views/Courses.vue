<template>
  <div class="courses-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>课程管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加课程
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索课程名称、课程代码或教师"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 课程表格 -->
      <el-table
        :data="courses"
        style="width: 100%"
        v-loading="loading"
        stripe
      >
        <el-table-column prop="course_code" label="课程代码" width="120" />
        <el-table-column prop="course_name" label="课程名称" width="200" />
        <el-table-column prop="credits" label="学分" width="80" />
        <el-table-column prop="teacher" label="授课教师" width="120" />
        <el-table-column prop="department" label="开课院系" />
        <el-table-column prop="semester" label="学期" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? '开课' : '停课' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewCourse(scope.row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editCourse(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteCourse(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑课程对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑课程' : '添加课程'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="courseFormRef"
        :model="courseForm"
        :rules="courseRules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="课程代码" prop="course_code">
              <el-input v-model="courseForm.course_code" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程名称" prop="course_name">
              <el-input v-model="courseForm.course_name" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学分" prop="credits">
              <el-input-number v-model="courseForm.credits" :min="0.5" :max="10" :step="0.5" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授课教师" prop="teacher">
              <el-input v-model="courseForm.teacher" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开课院系" prop="department">
              <el-input v-model="courseForm.department" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学期" prop="semester">
              <el-select v-model="courseForm.semester" style="width: 100%">
                <el-option label="2024春季" value="2024春季" />
                <el-option label="2024秋季" value="2024秋季" />
                <el-option label="2025春季" value="2025春季" />
                <el-option label="2025秋季" value="2025秋季" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="课程描述" prop="description">
          <el-input v-model="courseForm.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看课程详情对话框 -->
    <el-dialog v-model="showViewDialog" title="课程详情" width="600px">
      <div v-if="currentCourse" class="course-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程代码">{{ currentCourse.course_code }}</el-descriptions-item>
          <el-descriptions-item label="课程名称">{{ currentCourse.course_name }}</el-descriptions-item>
          <el-descriptions-item label="学分">{{ currentCourse.credits }}</el-descriptions-item>
          <el-descriptions-item label="授课教师">{{ currentCourse.teacher }}</el-descriptions-item>
          <el-descriptions-item label="开课院系">{{ currentCourse.department }}</el-descriptions-item>
          <el-descriptions-item label="学期">{{ currentCourse.semester }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentCourse.status === 'active' ? 'success' : 'info'">
              {{ currentCourse.status === 'active' ? '开课' : '停课' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="课程描述" :span="2">{{ currentCourse.description }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { getCourses, createCourse, updateCourse, deleteCourse as deleteCourseApi } from '@/api/course'

const loading = ref(false)
const submitLoading = ref(false)
const courses = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')

const showAddDialog = ref(false)
const showViewDialog = ref(false)
const isEdit = ref(false)
const currentCourse = ref(null)
const courseFormRef = ref()

const courseForm = reactive({
  course_code: '',
  course_name: '',
  credits: 1,
  teacher: '',
  department: '',
  semester: '',
  description: ''
})

const courseRules = {
  course_code: [
    { required: true, message: '请输入课程代码', trigger: 'blur' }
  ],
  course_name: [
    { required: true, message: '请输入课程名称', trigger: 'blur' }
  ],
  credits: [
    { required: true, message: '请输入学分', trigger: 'blur' }
  ],
  teacher: [
    { required: true, message: '请输入授课教师', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请输入开课院系', trigger: 'blur' }
  ],
  semester: [
    { required: true, message: '请选择学期', trigger: 'change' }
  ]
}

// 加载课程列表
const loadCourses = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    const response = await getCourses(params)
    courses.value = response.data.courses
    total.value = response.data.total
  } catch (error) {
    console.error('加载课程列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    loadCourses()
  }, 500)
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadCourses()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadCourses()
}

// 查看课程
const viewCourse = (course) => {
  currentCourse.value = course
  showViewDialog.value = true
}

// 编辑课程
const editCourse = (course) => {
  isEdit.value = true
  currentCourse.value = course
  Object.keys(courseForm).forEach(key => {
    courseForm[key] = course[key] || ''
  })
  showAddDialog.value = true
}

// 删除课程
const deleteCourse = async (course) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除课程 "${course.course_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteCourseApi(course.id)
    ElMessage.success('删除成功')
    loadCourses()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除课程失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!courseFormRef.value) return

  await courseFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        if (isEdit.value) {
          await updateCourse(currentCourse.value.id, courseForm)
          ElMessage.success('更新成功')
        } else {
          await createCourse(courseForm)
          ElMessage.success('添加成功')
        }
        showAddDialog.value = false
        loadCourses()
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  currentCourse.value = null
  Object.keys(courseForm).forEach(key => {
    if (key === 'credits') {
      courseForm[key] = 1
    } else {
      courseForm[key] = ''
    }
  })
  if (courseFormRef.value) {
    courseFormRef.value.clearValidate()
  }
}

onMounted(() => {
  loadCourses()
})
</script>

<style scoped>
.courses-page {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.course-detail {
  padding: 20px 0;
}
</style>
