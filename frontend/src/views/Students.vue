<template>
  <div class="students-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>学生管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加学生
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索学生姓名、学号或班级"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 学生表格 -->
      <el-table
        :data="students"
        style="width: 100%"
        v-loading="loading"
        stripe
      >
        <el-table-column prop="student_no" label="学号" width="120" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            {{ scope.row.gender === 'male' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column prop="class" label="班级" width="120" />
        <el-table-column prop="major" label="专业" />
        <el-table-column prop="phone" label="电话" width="130" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewStudent(scope.row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editStudent(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteStudent(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑学生对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑学生' : '添加学生'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="studentFormRef"
        :model="studentForm"
        :rules="studentRules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学号" prop="student_no">
              <el-input v-model="studentForm.student_no" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="studentForm.name" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="studentForm.gender" style="width: 100%">
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="age">
              <el-input-number v-model="studentForm.age" :min="16" :max="30" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="班级" prop="class">
              <el-input v-model="studentForm.class" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业" prop="major">
              <el-input v-model="studentForm.major" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="studentForm.phone" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入学年份" prop="enroll_year">
              <el-input-number v-model="studentForm.enroll_year" :min="2020" :max="2030" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="studentForm.email" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="studentForm.address" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
    <!-- 查看学生详情对话框 -->
    <el-dialog v-model="showViewDialog" title="学生详情" width="600px">
      <div v-if="currentStudent" class="student-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学号">{{ currentStudent.student_no }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ currentStudent.name }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentStudent.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentStudent.age }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ currentStudent.class }}</el-descriptions-item>
          <el-descriptions-item label="专业">{{ currentStudent.major }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ currentStudent.phone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentStudent.email }}</el-descriptions-item>
          <el-descriptions-item label="入学年份">{{ currentStudent.enroll_year }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentStudent.status)">
              {{ getStatusText(currentStudent.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="地址" :span="2">{{ currentStudent.address }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { getStudents, createStudent, updateStudent, deleteStudent as deleteStudentApi } from '@/api/student'

const loading = ref(false)
const submitLoading = ref(false)
const students = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')

const showAddDialog = ref(false)
const showViewDialog = ref(false)
const isEdit = ref(false)
const currentStudent = ref(null)
const studentFormRef = ref()

const studentForm = reactive({
  student_no: '',
  name: '',
  gender: 'male',
  age: 18,
  class: '',
  major: '',
  phone: '',
  email: '',
  address: '',
  enroll_year: new Date().getFullYear()
})

const studentRules = {
  student_no: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' }
  ],
  class: [
    { required: true, message: '请输入班级', trigger: 'blur' }
  ],
  major: [
    { required: true, message: '请输入专业', trigger: 'blur' }
  ]
}

// 加载学生列表
const loadStudents = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    const response = await getStudents(params)
    students.value = response.data.students
    total.value = response.data.total
  } catch (error) {
    console.error('加载学生列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    loadStudents()
  }, 500)
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadStudents()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadStudents()
}

// 查看学生
const viewStudent = (student) => {
  currentStudent.value = student
  showViewDialog.value = true
}

// 编辑学生
const editStudent = (student) => {
  isEdit.value = true
  currentStudent.value = student
  Object.keys(studentForm).forEach(key => {
    studentForm[key] = student[key] || ''
  })
  showAddDialog.value = true
}

// 删除学生
const deleteStudent = async (student) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学生 "${student.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteStudentApi(student.id)
    ElMessage.success('删除成功')
    loadStudents()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除学生失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!studentFormRef.value) return

  await studentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        if (isEdit.value) {
          await updateStudent(currentStudent.value.id, studentForm)
          ElMessage.success('更新成功')
        } else {
          await createStudent(studentForm)
          ElMessage.success('添加成功')
        }
        showAddDialog.value = false
        loadStudents()
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  currentStudent.value = null
  Object.keys(studentForm).forEach(key => {
    if (key === 'gender') {
      studentForm[key] = 'male'
    } else if (key === 'age') {
      studentForm[key] = 18
    } else if (key === 'enroll_year') {
      studentForm[key] = new Date().getFullYear()
    } else {
      studentForm[key] = ''
    }
  })
  if (studentFormRef.value) {
    studentFormRef.value.clearValidate()
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusTypes = {
    'active': 'success',
    'inactive': 'warning',
    'graduated': 'info'
  }
  return statusTypes[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusTexts = {
    'active': '在读',
    'inactive': '休学',
    'graduated': '毕业'
  }
  return statusTexts[status] || '未知'
}

onMounted(() => {
  loadStudents()
})
</script>

<style scoped>
.students-page {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.student-detail {
  padding: 20px 0;
}
</style>
