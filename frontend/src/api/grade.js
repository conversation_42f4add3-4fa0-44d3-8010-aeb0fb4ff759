import request from '@/utils/request'

// 获取成绩列表
export const getGrades = (params) => {
  return request({
    url: '/grades',
    method: 'get',
    params
  })
}

// 获取单个成绩信息
export const getGrade = (id) => {
  return request({
    url: `/grades/${id}`,
    method: 'get'
  })
}

// 创建成绩
export const createGrade = (data) => {
  return request({
    url: '/grades',
    method: 'post',
    data
  })
}

// 更新成绩信息
export const updateGrade = (id, data) => {
  return request({
    url: `/grades/${id}`,
    method: 'put',
    data
  })
}

// 删除成绩
export const deleteGrade = (id) => {
  return request({
    url: `/grades/${id}`,
    method: 'delete'
  })
}
