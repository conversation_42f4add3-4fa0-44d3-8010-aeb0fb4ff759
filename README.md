# 学生成绩管理系统

一个基于前后端分离架构的学生成绩管理系统，使用现代化技术栈构建，适合新手学习Web开发。项目采用分层架构设计，代码结构清晰，易于理解和扩展。

## ✨ 特色亮点

- 🏗️ **分层架构设计** - 配置层、数据层、控制层、路由层、中间件层分离
- 💾 **SQLite数据库** - 轻量级、无需安装、开箱即用
- 🔐 **JWT认证** - 安全的用户认证机制
- 📱 **响应式设计** - 支持多种设备访问
- 🎨 **现代化UI** - 基于Element Plus的美观界面
- 📊 **数据可视化** - 成绩统计和图表展示

## 🛠️ 技术栈

### 后端
- **Go 1.21+** - 高性能编程语言
- **Gin** - 轻量级Web框架
- **GORM** - 强大的ORM框架
- **SQLite** - 嵌入式数据库
- **JWT** - JSON Web Token认证

### 前端
- **Vue 3** - 渐进式前端框架
- **Element Plus** - Vue 3 UI组件库
- **Vite** - 下一代前端构建工具
- **Pinia** - Vue 3状态管理
- **Axios** - HTTP请求库

## 🚀 功能特性

### 核心功能
- 🔐 **用户认证** - 登录/注册，JWT Token认证
- 👥 **学生管理** - 学生信息的增删改查，支持批量操作
- 📚 **课程管理** - 课程信息维护，学分管理
- 📊 **成绩管理** - 成绩录入、修改、统计分析
- 🔍 **智能搜索** - 支持姓名、学号、班级等多字段搜索
- 📄 **分页展示** - 大数据量友好的分页显示

### 用户体验
- 📱 **响应式设计** - 完美适配PC、平板、手机
- 🎨 **现代化界面** - 简洁美观的用户界面
- ⚡ **快速响应** - 优化的前后端交互
- 🔄 **实时更新** - 数据变更即时反映

## 📁 项目结构

```
student-grade-system/
├── backend/                    # 后端服务 (Go + Gin + SQLite)
│   ├── main.go                # 🚀 程序入口，服务器启动
│   ├── config/                # ⚙️ 配置层
│   │   ├── database.go        #   数据库连接配置
│   │   └── init_data.go       #   初始数据生成
│   ├── models/                # 📊 数据模型层
│   │   ├── user.go           #   用户模型
│   │   ├── student.go        #   学生模型
│   │   ├── course.go         #   课程模型
│   │   └── grade.go          #   成绩模型
│   ├── controllers/           # 🎮 控制器层
│   │   ├── auth.go           #   认证控制器
│   │   ├── student.go        #   学生管理控制器
│   │   ├── course.go         #   课程管理控制器
│   │   └── grade.go          #   成绩管理控制器
│   ├── middleware/            # 🛡️ 中间件层
│   │   └── auth.go           #   JWT认证中间件
│   ├── routes/                # 🛣️ 路由层
│   │   └── routes.go         #   API路由配置
│   ├── utils/                 # 🔧 工具层
│   │   ├── jwt.go            #   JWT工具函数
│   │   └── password.go       #   密码加密工具
│   ├── database.db           # 💾 SQLite数据库文件
│   ├── go.mod                # 📦 Go模块依赖
│   └── go.sum                # 🔒 依赖版本锁定
└── frontend/                  # 前端应用 (Vue3 + Element Plus)
    ├── src/
    │   ├── components/        # 🧩 可复用组件
    │   │   └── Layout.vue    #   布局组件
    │   ├── views/            # 📄 页面组件
    │   │   ├── Login.vue     #   登录页面
    │   │   ├── Dashboard.vue #   仪表盘
    │   │   ├── Students.vue  #   学生管理
    │   │   ├── Courses.vue   #   课程管理
    │   │   └── Grades.vue    #   成绩管理
    │   ├── router/           # 🧭 路由配置
    │   │   └── index.js      #   Vue Router配置
    │   ├── store/            # 🗄️ 状态管理
    │   │   └── auth.js       #   认证状态管理
    │   ├── api/              # 🌐 API接口
    │   │   ├── auth.js       #   认证API
    │   │   ├── student.js    #   学生API
    │   │   ├── course.js     #   课程API
    │   │   └── grade.js      #   成绩API
    │   ├── utils/            # 🛠️ 工具函数
    │   │   └── request.js    #   HTTP请求封装
    │   ├── App.vue           # 🏠 根组件
    │   └── main.js           # 🚀 应用入口
    ├── public/               # 📂 静态资源
    ├── package.json          # 📦 前端依赖配置
    └── vite.config.js        # ⚡ Vite构建配置
```

## 🚀 快速开始

### 📋 环境要求

- **Go 1.21+** - 后端开发语言
- **Node.js 16+** - 前端开发环境
- **SQLite** - 数据库（内置，无需单独安装）

### 🔧 安装与启动

#### 1️⃣ 克隆项目
```bash
git clone <repository-url>
cd student-grade-system
```

#### 2️⃣ 启动后端服务

```bash
# 进入后端目录
cd backend

# 安装Go依赖
go mod tidy

# 启动后端服务
go run main.go
```

🎉 后端服务启动成功！访问地址：`http://localhost:8000`

#### 3️⃣ 启动前端服务

```bash
# 进入前端目录（新开终端窗口）
cd frontend

# 安装前端依赖
npm install

# 启动开发服务器
npm run dev
```

🎉 前端服务启动成功！访问地址：`http://localhost:3001`

### 🎯 首次使用

1. **打开浏览器** 访问 `http://localhost:3001`
2. **使用默认账号登录**：
   - 用户名：`admin`
   - 密码：`123456`
3. **开始体验** 完整的学生成绩管理功能！

### 🗄️ 数据库配置

SQLite数据库会自动创建在 `backend/database.db`。如需自定义路径：

```bash
# 设置环境变量
export DB_PATH=./custom_database.db

# 或在启动时指定
DB_PATH=./custom_database.db go run main.go
```

## 📊 预置数据说明

系统启动时会自动初始化以下测试数据：

### 👤 管理员账户
- **用户名**: `admin`
- **密码**: `123456`
- **角色**: 管理员

### 🎓 示例学生 (4名)
| 学号 | 姓名 | 性别 | 班级 | 专业 | 地址 |
|------|------|------|------|------|------|
| 2024001 | 张三 | 男 | 计算机1班 | 计算机科学 | 北京市海淀区 |
| 2024002 | 李四 | 女 | 计算机1班 | 计算机科学 | 上海市浦东新区 |
| 2024003 | 王五 | 男 | 计算机2班 | 软件工程 | 广州市天河区 |
| 2024004 | 赵六 | 女 | 计算机2班 | 软件工程 | 深圳市南山区 |

### 📚 示例课程 (5门)
| 课程代码 | 课程名称 | 学分 | 授课教师 | 学期 |
|----------|----------|------|----------|------|
| CS101 | 计算机基础 | 3.0 | 张教授 | 2024春季 |
| CS102 | 数据结构 | 4.0 | 李教授 | 2024春季 |
| CS103 | 数据库原理 | 3.5 | 王教授 | 2024春季 |
| CS104 | 操作系统 | 4.0 | 陈教授 | 2024秋季 |
| CS105 | 计算机网络 | 3.5 | 刘教授 | 2024秋季 |

### 📈 示例成绩 (8条)
包含不同等级的成绩记录，自动计算等级（A/B/C/D/F），包含考试日期和详细备注。

> 💡 **提示**: 首次启动后，这些数据仅初始化一次。如需重新初始化，请删除 `database.db` 文件后重启服务。

## 📡 API接口文档

### 🔐 认证接口
| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| `POST` | `/api/v1/auth/login` | 用户登录 | ❌ |
| `POST` | `/api/v1/auth/register` | 用户注册 | ❌ |

### 👥 学生管理接口
| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| `GET` | `/api/v1/students` | 获取学生列表（支持分页、搜索） | ✅ |
| `GET` | `/api/v1/students/:id` | 获取学生详情 | ✅ |
| `POST` | `/api/v1/students` | 创建学生 | ✅ |
| `PUT` | `/api/v1/students/:id` | 更新学生信息 | ✅ |
| `DELETE` | `/api/v1/students/:id` | 删除学生 | ✅ |
| `GET` | `/api/v1/student-grades/:student_id` | 获取学生成绩 | ✅ |

### 📚 课程管理接口
| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| `GET` | `/api/v1/courses` | 获取课程列表（支持分页、搜索） | ✅ |
| `GET` | `/api/v1/courses/:id` | 获取课程详情 | ✅ |
| `POST` | `/api/v1/courses` | 创建课程 | ✅ |
| `PUT` | `/api/v1/courses/:id` | 更新课程信息 | ✅ |
| `DELETE` | `/api/v1/courses/:id` | 删除课程 | ✅ |

### 📊 成绩管理接口
| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| `GET` | `/api/v1/grades` | 获取成绩列表（支持筛选） | ✅ |
| `GET` | `/api/v1/grades/:id` | 获取成绩详情 | ✅ |
| `POST` | `/api/v1/grades` | 录入成绩 | ✅ |
| `PUT` | `/api/v1/grades/:id` | 更新成绩 | ✅ |
| `DELETE` | `/api/v1/grades/:id` | 删除成绩 | ✅ |

### 📝 请求示例

#### 登录请求
```bash
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

#### 获取学生列表（需要认证）
```bash
curl -X GET http://localhost:8000/api/v1/students \
  -H "Authorization: Bearer <your-jwt-token>"
```

## 🗃️ 数据库设计

### 📊 数据表结构

#### 👤 用户表 (users)
| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | INTEGER | 主键 | PRIMARY KEY |
| username | TEXT | 用户名 | UNIQUE, NOT NULL |
| password | TEXT | 密码（MD5加密） | NOT NULL |
| email | TEXT | 邮箱 | UNIQUE |
| role | TEXT | 角色（admin/teacher） | DEFAULT 'admin' |
| created_at | DATETIME | 创建时间 | |
| updated_at | DATETIME | 更新时间 | |

#### 🎓 学生表 (students)
| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | INTEGER | 主键 | PRIMARY KEY |
| student_no | TEXT | 学号 | UNIQUE, NOT NULL |
| name | TEXT | 姓名 | NOT NULL |
| gender | TEXT | 性别 | DEFAULT 'male' |
| age | INTEGER | 年龄 | |
| class | TEXT | 班级 | |
| major | TEXT | 专业 | |
| phone | TEXT | 电话 | |
| email | TEXT | 邮箱 | |
| address | TEXT | 地址 | |
| enroll_year | INTEGER | 入学年份 | |
| status | TEXT | 状态 | DEFAULT 'active' |
| created_at | DATETIME | 创建时间 | |
| updated_at | DATETIME | 更新时间 | |

#### 📚 课程表 (courses)
| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | INTEGER | 主键 | PRIMARY KEY |
| course_code | TEXT | 课程代码 | UNIQUE, NOT NULL |
| course_name | TEXT | 课程名称 | NOT NULL |
| credits | REAL | 学分 | NOT NULL |
| teacher | TEXT | 授课教师 | |
| department | TEXT | 开课院系 | |
| semester | TEXT | 学期 | |
| description | TEXT | 课程描述 | |
| status | TEXT | 状态 | DEFAULT 'active' |
| created_at | DATETIME | 创建时间 | |
| updated_at | DATETIME | 更新时间 | |

#### 📈 成绩表 (grades)
| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | INTEGER | 主键 | PRIMARY KEY |
| student_id | INTEGER | 学生ID | FOREIGN KEY, NOT NULL |
| course_id | INTEGER | 课程ID | FOREIGN KEY, NOT NULL |
| score | REAL | 分数 | NOT NULL |
| grade_level | TEXT | 等级（A/B/C/D/F） | |
| exam_type | TEXT | 考试类型 | DEFAULT 'final' |
| exam_date | DATETIME | 考试日期 | |
| remarks | TEXT | 备注 | |
| created_at | DATETIME | 创建时间 | |
| updated_at | DATETIME | 更新时间 | |

### 🔗 数据关系
- `grades.student_id` → `students.id` (多对一)
- `grades.course_id` → `courses.id` (多对一)
- 一个学生可以有多门课程的成绩
- 一门课程可以有多个学生的成绩

## 📚 学习要点

### 🔧 后端技术学习路径

#### 🐹 Go语言基础
- **语法特性** - 变量、函数、结构体、接口
- **并发编程** - Goroutine、Channel
- **包管理** - Go Modules、依赖管理
- **错误处理** - Error接口、错误传播

#### 🌐 Gin Web框架
- **路由系统** - RESTful路由、路由分组
- **中间件** - 认证、CORS、日志记录
- **请求处理** - 参数绑定、数据验证
- **响应格式** - JSON响应、错误处理

#### 🗄️ GORM数据库操作
- **模型定义** - 结构体标签、关联关系
- **CRUD操作** - 增删改查、批量操作
- **查询构建** - 条件查询、分页、排序
- **数据迁移** - 自动迁移、表结构管理

#### 🔐 JWT认证机制
- **Token生成** - 用户信息编码、过期时间
- **Token验证** - 中间件验证、权限控制
- **安全考虑** - 密钥管理、Token刷新

### 🎨 前端技术学习路径

#### ⚡ Vue 3核心概念
- **组合式API** - setup()、ref()、reactive()
- **响应式系统** - 数据绑定、计算属性、侦听器
- **组件通信** - Props、Emit、Provide/Inject
- **生命周期** - 组件生命周期钩子

#### 🎯 Element Plus UI库
- **组件使用** - 表格、表单、对话框、分页
- **主题定制** - CSS变量、样式覆盖
- **图标系统** - 图标使用、自定义图标
- **响应式布局** - 栅格系统、断点适配

#### 🧭 Vue Router路由管理
- **路由配置** - 路由定义、嵌套路由
- **导航守卫** - 全局守卫、路由守卫
- **动态路由** - 参数传递、路由匹配
- **编程式导航** - 路由跳转、历史管理

#### 🗄️ Pinia状态管理
- **Store定义** - 状态、动作、计算属性
- **数据持久化** - localStorage集成
- **模块化** - 多Store管理
- **TypeScript支持** - 类型安全

#### 🌐 Axios HTTP客户端
- **请求配置** - 基础URL、超时设置
- **拦截器** - 请求拦截、响应拦截
- **错误处理** - 统一错误处理、重试机制
- **认证集成** - Token自动添加

## 🚀 扩展功能建议

### 📊 数据分析功能
- [ ] **成绩统计图表** - 使用ECharts展示成绩分布、趋势分析
- [ ] **数据导入导出** - Excel文件导入导出，批量数据处理
- [ ] **成绩报告生成** - PDF成绩单、统计报告自动生成
- [ ] **数据可视化** - 班级对比、课程难度分析

### 🔔 通知与提醒
- [ ] **邮件通知功能** - 成绩录入通知、系统消息推送
- [ ] **短信提醒** - 重要通知短信发送
- [ ] **站内消息** - 系统内消息中心
- [ ] **定时任务** - 自动备份、定期报告

### 👥 用户体验优化
- [ ] **多角色权限** - 学生、教师、管理员不同权限
- [ ] **个人中心** - 用户资料管理、密码修改
- [ ] **主题切换** - 深色模式、多主题支持
- [ ] **多语言支持** - 国际化i18n

### 🔧 系统功能增强
- [ ] **文件上传功能** - 头像上传、附件管理
- [ ] **操作日志记录** - 用户操作审计、系统日志
- [ ] **数据备份恢复** - 自动备份、一键恢复
- [ ] **API文档** - Swagger自动生成API文档

### 📱 移动端支持
- [ ] **PWA支持** - 离线访问、桌面安装
- [ ] **移动端优化** - 触摸友好、响应式改进
- [ ] **小程序版本** - 微信小程序适配

### 🔒 安全性增强
- [ ] **双因子认证** - 2FA安全登录
- [ ] **密码策略** - 密码强度要求、定期更换
- [ ] **访问控制** - IP白名单、访问频率限制
- [ ] **数据加密** - 敏感数据加密存储

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是新功能、Bug修复、文档改进还是问题反馈。

### 📝 如何贡献

1. **🍴 Fork 项目** - 点击右上角Fork按钮
2. **🌿 创建功能分支** - `git checkout -b feature/amazing-feature`
3. **💾 提交更改** - `git commit -m 'Add some amazing feature'`
4. **📤 推送到分支** - `git push origin feature/amazing-feature`
5. **🔄 创建 Pull Request** - 提交PR并描述你的更改

### 🐛 问题反馈

- 发现Bug？请创建 [Issue](../../issues)
- 有功能建议？欢迎讨论
- 需要帮助？查看文档或提问

### 📋 开发规范

- **代码风格** - 遵循Go和Vue的官方规范
- **提交信息** - 使用清晰的提交信息
- **测试覆盖** - 为新功能添加测试
- **文档更新** - 更新相关文档

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📞 联系方式

- 📧 **邮箱**: [<EMAIL>](mailto:<EMAIL>)
- 🐛 **问题反馈**: [GitHub Issues](../../issues)
- 💬 **讨论交流**: [GitHub Discussions](../../discussions)

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！

🎯 **适合人群**: Go语言学习者、Vue3初学者、全栈开发入门者

📖 **学习建议**: 建议按照技术栈逐步学习，先掌握基础概念再深入实践
