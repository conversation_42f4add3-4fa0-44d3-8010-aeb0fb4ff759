version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: student_grade_backend
    ports:
      - "8000:8000"
    environment:
      DB_PATH: /app/data/database.db
    volumes:
      - ./backend:/app
      - sqlite_data:/app/data
    working_dir: /app

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: student_grade_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    working_dir: /app

volumes:
  sqlite_data:
